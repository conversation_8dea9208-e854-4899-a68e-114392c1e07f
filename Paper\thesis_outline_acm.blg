This is BibTeX, Version 0.99d (TeX Live 2025)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: thesis_outline_acm.aux
The style file: ACM-Reference-Format.bst
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 6000 items from 3000.
Database file #1: references.bib
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated glb_str_ptr (elt_size=4) to 20 items from 10.
Reallocated global_strs (elt_size=200001) to 20 items from 10.
Reallocated glb_str_end (elt_size=4) to 20 items from 10.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 9000 items from 6000.
You've used 5 entries,
            6056 wiz_defined-function locations,
            1515 strings with 18644 characters,
and the built_in function-call counts, 4724 in all, are:
= -- 649
> -- 113
< -- 0
+ -- 35
- -- 44
* -- 285
:= -- 456
add.period$ -- 25
call.type$ -- 5
change.case$ -- 28
chr.to.int$ -- 5
cite$ -- 5
duplicate$ -- 407
empty$ -- 292
format.name$ -- 52
if$ -- 1126
int.to.chr$ -- 2
int.to.str$ -- 1
missing$ -- 8
newline$ -- 96
num.names$ -- 35
pop$ -- 130
preamble$ -- 1
purify$ -- 68
quote$ -- 0
skip$ -- 157
stack$ -- 0
substring$ -- 362
swap$ -- 34
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 122
warning$ -- 0
while$ -- 37
width$ -- 0
write$ -- 144
